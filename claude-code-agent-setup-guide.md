# 🚀 Claude Code Agent Setup Guide

> Your complete guide to setting up and using agents in Claude Code, with examples from popular GitHub templates

## 📚 Table of Contents

1. [Introduction](#introduction)
2. [Quick Start](#quick-start)
3. [Creating Your First Agent](#creating-your-first-agent)
4. [Popular Agent Templates](#popular-agent-templates)
5. [Advanced Configuration](#advanced-configuration)
6. [Using Claude Flow](#using-claude-flow)
7. [Troubleshooting](#troubleshooting)
8. [Resources](#resources)

## Introduction

Claude Code agents (also called sub-agents) are specialized AI assistants that enhance <PERSON>'s capabilities by providing task-specific expertise. They act as dedicated helpers that <PERSON> can call upon when encountering particular types of work.

### Key Benefits
- **Task Specialization**: Each agent is an expert in a specific domain
- **Context Isolation**: Agents operate in isolated contexts, preventing cross-contamination
- **Parallel Execution**: Multiple agents can work simultaneously
- **Reusability**: Share agents across projects and teams

## Quick Start

### Step 1: Create the Agent Directory

```bash
# Create the .claude directory in your project root
mkdir -p .claude/agents

# For user-level agents (available in all projects)
mkdir -p ~/.claude/agents
```

### Step 2: Create Your First Agent

Create a file `.claude/agents/code-reviewer.md`:

```yaml
---
name: code-reviewer
description: Use proactively to review code changes for quality, security, and best practices
tools:
  - Read
  - Grep
  - WebSearch
---

You are an expert code reviewer with deep knowledge of software engineering best practices.

## Your Responsibilities

1. **Code Quality**: Review for readability, maintainability, and adherence to SOLID principles
2. **Security**: Identify potential vulnerabilities and security issues
3. **Performance**: Spot performance bottlenecks and optimization opportunities
4. **Best Practices**: Ensure code follows language-specific conventions

## Review Process

When reviewing code:
1. First understand the context and purpose
2. Check for logical errors and edge cases
3. Verify error handling and input validation
4. Assess test coverage
5. Provide constructive feedback with specific examples

## Output Format

Provide feedback in this structure:
- **Summary**: Brief overview of the review
- **Critical Issues**: Must-fix problems
- **Suggestions**: Improvements for better code quality
- **Positive Aspects**: What was done well
```

### Step 3: Use the Agent

In Claude Code, you can now:

1. **Use the `/agents` command** to see all available agents
2. **Mention the agent** with `@code-reviewer` in your prompt
3. **Claude will automatically invoke** the agent when relevant

Example usage:
```
@code-reviewer please review the changes in src/api/handler.js
```

## Creating Your First Agent

### Basic Agent Structure

Every agent file has two parts:

1. **YAML Frontmatter**: Metadata and configuration
2. **Instructions**: The agent's role and behavior

```yaml
---
name: your-agent-name
description: What this agent does (be specific for auto-activation)
tools:  # Optional - if omitted, inherits all available tools
  - Read
  - Write
  - Edit
  - Bash
---

# Agent Instructions

You are a [specific role]. Your expertise includes...

## Core Responsibilities
[List main tasks]

## Workflow
[Step-by-step process]

## Best Practices
[Guidelines to follow]
```

## Popular Agent Templates

### 1. Test Runner Agent

`.claude/agents/test-runner.md`:

```yaml
---
name: test-runner
description: Use PROACTIVELY to run tests after code changes and fix failures
tools:
  - Bash
  - Read
  - Edit
  - MultiEdit
---

You are a test automation expert. When you see code changes, immediately:

1. Identify relevant test files
2. Run appropriate test commands (npm test, pytest, etc.)
3. Analyze failures and provide fixes
4. Re-run tests to verify fixes

Always ensure all tests pass before marking a task complete.
```

### 2. Documentation Writer

`.claude/agents/doc-writer.md`:

```yaml
---
name: doc-writer
description: Creates and updates documentation for code changes
tools:
  - Read
  - Write
  - Grep
---

You are a technical documentation specialist.

## Documentation Standards

1. **README.md**: Keep project overview, setup, and usage current
2. **API Docs**: Document all public interfaces with examples
3. **Code Comments**: Add JSDoc/docstrings for complex functions
4. **Architecture Docs**: Update design decisions and diagrams

## Writing Style

- Clear and concise
- Include code examples
- Explain the "why" not just the "what"
- Keep language accessible to newcomers
```

### 3. Security Auditor

`.claude/agents/security-auditor.md`:

```yaml
---
name: security-auditor
description: MUST BE USED for security analysis and vulnerability detection
tools:
  - Read
  - Grep
  - WebSearch
---

You are a cybersecurity expert specializing in application security.

## Security Checklist

### Input Validation
- SQL injection prevention
- XSS protection
- Command injection safeguards

### Authentication & Authorization
- Secure password handling
- JWT validation
- Permission checks

### Data Protection
- Encryption at rest and in transit
- PII handling compliance
- Secure key management

### Dependencies
- Check for known vulnerabilities
- License compliance
- Supply chain security

Report findings with CVSS scores and remediation steps.
```

### 4. Performance Optimizer

`.claude/agents/performance-optimizer.md`:

```yaml
---
name: performance-optimizer
description: Analyzes and optimizes code performance
tools:
  - Read
  - Edit
  - Bash
---

You are a performance engineering specialist.

## Optimization Areas

1. **Algorithm Complexity**: Identify O(n²) or worse patterns
2. **Database Queries**: Prevent N+1 queries, add indexes
3. **Caching**: Implement appropriate caching strategies
4. **Memory Usage**: Detect memory leaks and optimize allocations
5. **Async Operations**: Proper use of async/await and parallel processing

## Benchmarking

Always measure before and after optimizations:
- Use appropriate profiling tools
- Document performance gains
- Consider trade-offs (readability vs performance)
```

## Advanced Configuration

### Using Claude Flow Agents

Claude Flow provides 54+ specialized agents. To use them:

```bash
# Install Claude Flow
npm install -g claude-flow@alpha

# Initialize with MCP
npx claude-flow@alpha init --force

# Spawn an agent
npx claude-flow@alpha agent spawn --type coder --name "backend-dev"

# Or use swarm mode for complex tasks
npx claude-flow@alpha swarm "build a REST API with authentication"
```

### Available Claude Flow Agent Types

- **Core**: `coder`, `reviewer`, `tester`, `planner`, `researcher`
- **Specialized**: `backend-dev`, `mobile-dev`, `ml-developer`, `api-docs`
- **Architecture**: `system-architect`, `repo-architect`, `sparc-architect`
- **DevOps**: `cicd-engineer`, `security-manager`, `performance-benchmarker`
- **GitHub**: `pr-manager`, `issue-tracker`, `release-manager`

### Creating Custom Commands

Create `.claude/commands/deploy.md`:

```markdown
---
name: deploy
description: Deploy the application to production
---

Deploy the application following these steps:

1. Run all tests
2. Build the production bundle
3. Update version number
4. Create git tag
5. Push to production branch
6. Monitor deployment logs

Target environment: $ARGUMENTS
```

Usage: `/project:deploy staging`

## Using Claude Flow

### Hierarchical Swarm for Development

```bash
# Initialize hierarchical topology (best for structured development)
npx claude-flow@alpha swarm init \
  --topology hierarchical \
  --max-agents 10 \
  --strategy parallel \
  --auto-spawn

# Execute with swarm
npx claude-flow@alpha swarm "implement user authentication with JWT"
```

### Mesh Topology for Research

```bash
# Initialize mesh topology (best for exploration)
npx claude-flow@alpha swarm init \
  --topology mesh \
  --max-agents 5 \
  --strategy balanced

# Research task
npx claude-flow@alpha swarm "research best practices for microservices"
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Agent Not Detected

**Problem**: Claude doesn't recognize your agent

**Solutions**:
- Ensure file is in `.claude/agents/` directory
- Check YAML frontmatter syntax (use `---` delimiters)
- Verify agent name contains only lowercase letters, numbers, and hyphens
- Restart Claude Code after adding new agents

#### 2. Agent Not Auto-Activating

**Problem**: Agent doesn't activate automatically

**Solutions**:
- Make description more specific and action-oriented
- Include keywords like "PROACTIVELY" or "MUST BE USED"
- Use explicit mentions with `@agent-name`

#### 3. Tool Access Issues

**Problem**: Agent can't access needed tools

**Solutions**:
- Explicitly list required tools in YAML frontmatter
- Check tool names match exactly (case-sensitive)
- Verify MCP servers are connected for MCP tools

#### 4. API Key Errors with Claude Flow

**Problem**: "Invalid API key" when using Claude Flow

**Solutions**:
```bash
# Set your Anthropic API key
export ANTHROPIC_API_KEY="your-key-here"

# Or add to ~/.zshrc or ~/.bashrc
echo 'export ANTHROPIC_API_KEY="your-key-here"' >> ~/.zshrc
source ~/.zshrc
```

#### 5. Context Overflow

**Problem**: Agent operations cause context limit errors

**Solutions**:
- Break tasks into smaller chunks
- Use focused agents with specific tools
- Clear context between major operations
- Use `/clear` command periodically

### Debug Commands

```bash
# Check Claude Flow status
npx claude-flow@alpha status

# List available agents
npx claude-flow@alpha agent list

# View agent metrics
npx claude-flow@alpha agent metrics

# Check swarm status
npx claude-flow@alpha swarm status
```

## Resources

### GitHub Repositories

1. **[ruvnet/claude-flow](https://github.com/ruvnet/claude-flow)**
   - Official Claude Flow repository
   - 87 MCP tools, swarm intelligence, neural patterns

2. **[hesreallyhim/awesome-claude-code](https://github.com/hesreallyhim/awesome-claude-code)**
   - Curated list of commands, files, and workflows

3. **[VoltAgent/awesome-claude-code-subagents](https://github.com/VoltAgent/awesome-claude-code-subagents)**
   - 100+ production-ready specialized agents

4. **[wshobson/agents](https://github.com/wshobson/agents)**
   - 56 battle-tested subagents for various domains

5. **[vijaythecoder/awesome-claude-agents](https://github.com/vijaythecoder/awesome-claude-agents)**
   - Orchestrated sub-agent dev team

### Documentation

- [Anthropic Claude Code Docs](https://docs.anthropic.com/en/docs/claude-code)
- [Claude Code Best Practices](https://www.anthropic.com/engineering/claude-code-best-practices)
- [MCP Protocol Specification](https://modelcontextprotocol.io/)

### Community

- [Claude Flow Wiki](https://github.com/ruvnet/claude-flow/wiki)
- [Discord Community](https://discord.gg/anthropic)
- [GitHub Discussions](https://github.com/ruvnet/claude-flow/discussions)

## Next Steps

1. **Start Simple**: Create a basic agent for a specific task
2. **Iterate**: Refine instructions based on performance
3. **Share**: Contribute your agents to the community
4. **Scale**: Use Claude Flow for complex multi-agent workflows

---

*Remember: Agents are force multipliers. Start with one, master it, then build your army.*

**Happy Coding! 🚀**