# Claude Code Sub-Agents Feature Guide

## What are Sub-Agents?

Sub-agents are specialized AI assistants within Claude Code that handle specific tasks with their own context and configuration.

### Key Points

• **Independent Context** - Each sub-agent has its own context window, separate from the main conversation
• **Specialized Focus** - Designed for specific tasks like code review, testing, or debugging
• **Custom Configuration** - Each sub-agent can have unique tools and system prompts
• **Parallel Processing** - Run up to 10 sub-agents simultaneously

## How to Create Sub-Agents

### Quick Setup
1. Run `/agents` command in Claude Code
2. Select "Create New Agent"
3. Configure:
   - Name
   - Description
   - Tool permissions (optional)
   - System prompt

### File Structure
Sub-agents are stored as markdown files with YAML frontmatter:

```yaml
---
name: code-reviewer
description: Reviews code for quality and best practices
tools:
  - read_file
  - grep
---

# System Prompt
You are a code review specialist...
```

### Storage Locations
• **Project-level**: `.claude/agents/` (takes priority)
• **User-level**: `~/.claude/agents/`

## How to Use Sub-Agents

### Automatic Invocation
Claude Code automatically delegates to appropriate sub-agents based on task context

### Manual Invocation
Use explicit commands:
```
"Use the code-reviewer subagent to review this function"
```

## Benefits

• **No Context Switching** - Main conversation stays clean
• **Task Specialization** - Each agent excels at specific tasks
• **Reusability** - Use across multiple projects
• **Team Simulation** - Build your own AI development team

## Popular Sub-Agent Types

### Development
• **Frontend Developer** - UI/UX implementation
• **Backend Developer** - API and server logic
• **Database Expert** - SQL and data modeling
• **DevOps Engineer** - CI/CD and deployment

### Quality & Testing
• **Code Reviewer** - Code quality checks
• **Test Writer** - Unit and integration tests
• **Debugger** - Bug fixing specialist
• **Security Auditor** - Vulnerability scanning

### Specialized Tasks
• **Documentation Writer** - README and docs
• **Performance Optimizer** - Speed improvements
• **Refactoring Expert** - Code cleanup
• **Research Assistant** - Technical research

## Best Practices

• **Single Purpose** - Keep each sub-agent focused on one area
• **Clear Prompts** - Write detailed system prompts
• **Limited Tools** - Only grant necessary tool access
• **Version Control** - Commit project sub-agents to Git
• **Test First** - Verify sub-agent behavior before heavy use

## Quick Example

Create a test writer sub-agent:

```yaml
---
name: test-writer
description: Writes comprehensive test suites
tools:
  - read_file
  - write_file
  - run_command
---

# System Prompt
You are a test writing specialist. Focus on:
- Unit tests with high coverage
- Edge cases and error handling
- Clear test descriptions
- Following project testing conventions
```

## Getting Started

1. Update Claude Code to latest version
2. Run `/agents` to see available sub-agents
3. Create your first custom sub-agent
4. Test with a simple task
5. Iterate and improve

## Resources

• Official collections available on GitHub
• Community-maintained sub-agents
• 100+ pre-built specialized agents
• Active development and updates

---

*Sub-agents feature available in Claude Code v1.0.70+*