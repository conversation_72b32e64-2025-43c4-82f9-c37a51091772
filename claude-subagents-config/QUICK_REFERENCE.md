# Claude Code Subagents Quick Reference

## 🚀 Installation
```bash
cp -r claude-subagents-config/.claude /path/to/your/main/project/
cd /path/to/your/main/project
claude /agents
```

## 📋 Available Subagents

### 🏗️ Phase-Specific Specialists

| Subagent | Phase | Primary Use |
|----------|-------|-------------|
| `python-microservice-architect` | 1 | Create Python microservice structure |
| `grpc-communication-specialist` | 1 | Setup gRPC communication layer |
| `langchain-orchestrator` | 2 | Implement LangChain AI orchestration |
| `intelligent-router` | 2 | Create ML-powered service routing |
| `workflow-enhancer` | 3 | Enhance visual workflow builder |
| `queue-bridge-specialist` | 3 | Implement Celery-Bull bridge |
| `ml-analytics-specialist` | 4 | Upgrade predictive analytics |
| `monitoring-architect` | 4 | Build hybrid monitoring system |
| `docker-deployment-expert` | 5 | Create Docker Compose config |
| `migration-specialist` | 5 | Handle migration strategies |
| `performance-optimizer` | 6 | Optimize hybrid architecture |
| `integration-tester` | 6 | Comprehensive testing |

### 🔄 Cross-Cutting Specialists

| Subagent | Scope | Primary Use |
|----------|-------|-------------|
| `hybrid-architecture-coordinator` | All Phases | Manage Node.js/Python integration |
| `ai-services-orchestrator` | All Phases | Coordinate 8 AI services |
| `security-auditor` | All Phases | Security across both stacks |
| `infrastructure-manager` | All Phases | Manage databases and queues |
| `documentation-specialist` | All Phases | Maintain comprehensive docs |

## ⚡ Quick Commands

### Phase 1: Foundation Setup
```bash
# Create Python microservice
claude "Use the python-microservice-architect to create the Python microservice structure"

# Setup gRPC communication
claude "Use the grpc-communication-specialist to create gRPC communication layer"

# Validate integration
claude "Have the hybrid-architecture-coordinator review Phase 1 integration"
```

### Phase 2: LangChain Integration
```bash
# Implement LangChain orchestration
claude "Use the langchain-orchestrator to implement LangChain orchestration for the 8 AI services"

# Integrate AI services
claude "Use the ai-services-orchestrator to integrate all 8 AI services"
```

### Phase 3: Workflow Intelligence
```bash
# Enhance workflow builder
claude "Use the workflow-enhancer to add Python-powered intelligence to ReactFlow"

# Create queue bridge
claude "Use the queue-bridge-specialist to implement Celery-Bull bridge"
```

### Phase 4: Analytics Enhancement
```bash
# Upgrade analytics
claude "Use the ml-analytics-specialist to upgrade from TensorFlow.js to Python ML"

# Build monitoring
claude "Use the monitoring-architect to create hybrid monitoring system"
```

### Phase 5: Production Deployment
```bash
# Create Docker config
claude "Use the docker-deployment-expert to create production Docker Compose"

# Security audit
claude "Have the security-auditor perform comprehensive security audit"

# Migration strategy
claude "Use the migration-specialist to create migration and rollback strategies"
```

### Phase 6: Optimization
```bash
# Performance optimization
claude "Use the performance-optimizer to optimize hybrid architecture performance"

# Integration testing
claude "Use the integration-tester to create comprehensive integration tests"
```

## 🔧 Troubleshooting Commands

### General Issues
```bash
# Check subagent status
claude "/agents"

# Architecture review
claude "Have the hybrid-architecture-coordinator diagnose the integration issue"

# Security check
claude "Have the security-auditor identify security vulnerabilities"

# Performance analysis
claude "Use the performance-optimizer to identify current bottlenecks"
```

### Specific Problems
```bash
# Database connection issues
claude "Use the infrastructure-manager to diagnose database connectivity problems"

# AI service failures
claude "Use the ai-services-orchestrator to diagnose AI service integration issues"

# Docker deployment problems
claude "Use the docker-deployment-expert to troubleshoot Docker Compose issues"

# gRPC communication failures
claude "Use the grpc-communication-specialist to debug gRPC communication problems"
```

## 🎯 Multi-Agent Workflows

### Complete Feature Implementation
```bash
claude "First use the python-microservice-architect to add a new endpoint, then have the security-auditor review it, and finally use the performance-optimizer to ensure it meets performance requirements"
```

### Cross-Phase Validation
```bash
claude "Have the hybrid-architecture-coordinator review the integration between LangChain orchestration and Docker deployment configuration"
```

### Problem Resolution Chain
```bash
claude "Use the security-auditor to identify security issues, then have the performance-optimizer address any performance impacts of the security fixes"
```

## 📊 Success Criteria by Phase

### Phase 1 ✅
- Python microservice running (port 8001)
- gRPC communication operational
- Health checks passing
- Database connections working

### Phase 2 ✅
- LangChain orchestration functional
- All 8 AI services integrated
- Service routing working
- Memory systems operational

### Phase 3 ✅
- Workflow builder enhanced
- Celery-Bull bridge operational
- Real-time suggestions working
- Queue integration stable

### Phase 4 ✅
- Python ML models deployed
- Analytics upgrade complete
- Monitoring system operational
- Performance metrics available

### Phase 5 ✅
- Docker Compose working
- Security audit passed
- Migration strategy tested
- Production deployment ready

### Phase 6 ✅
- Performance optimized
- Integration tests passing
- Scaling strategies implemented
- System production-ready

## 🔍 Monitoring Commands

### Progress Tracking
```bash
# Check implementation status
claude "Show current implementation progress from Memory Bank"

# Phase completion status
claude "Have the hybrid-architecture-coordinator provide Phase [X] completion status"

# Performance metrics
claude "Use the performance-optimizer to show current performance metrics"
```

### Health Checks
```bash
# Overall system health
claude "Have the infrastructure-manager check overall system health"

# Security status
claude "Have the security-auditor provide current security status"

# AI services status
claude "Use the ai-services-orchestrator to check all AI services status"
```

## 🛠️ Configuration Management

### Environment Setup
```bash
# Development environment
claude "Use the docker-deployment-expert to set up development environment"

# Production configuration
claude "Use the docker-deployment-expert to create production configuration"

# Security configuration
claude "Have the security-auditor configure security settings"
```

### Service Configuration
```bash
# AI services configuration
claude "Use the ai-services-orchestrator to configure AI service integrations"

# Database configuration
claude "Use the infrastructure-manager to optimize database configuration"

# Cache configuration
claude "Use the performance-optimizer to configure Redis caching strategies"
```

## 📚 Documentation Commands

### Generate Documentation
```bash
# Architecture documentation
claude "Have the hybrid-architecture-coordinator generate architecture documentation"

# API documentation
claude "Use the documentation-specialist to create API documentation"

# Deployment documentation
claude "Use the docker-deployment-expert to create deployment documentation"
```

### Update Documentation
```bash
# Update after changes
claude "Have the documentation-specialist update documentation after Phase [X] completion"

# Security documentation
claude "Have the security-auditor update security documentation"
```

## 🚨 Emergency Procedures

### System Issues
```bash
# Emergency diagnosis
claude "Have the hybrid-architecture-coordinator perform emergency system diagnosis"

# Immediate security response
claude "Have the security-auditor perform immediate security incident response"

# Performance emergency
claude "Use the performance-optimizer for emergency performance analysis"
```

### Rollback Procedures
```bash
# Emergency rollback
claude "Use the migration-specialist to perform emergency rollback to previous version"

# Service isolation
claude "Use the infrastructure-manager to isolate failing services"
```

---

**Keep this reference handy for quick access to your AI development team!** 🚀
