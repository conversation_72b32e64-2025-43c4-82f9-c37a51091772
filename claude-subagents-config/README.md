# Claude Code Subagents Configuration for Hybrid Node.js/Python Architecture

## Overview

This directory contains specialized Claude Code subagents designed for your hybrid Node.js/Python architecture implementation. Each subagent is optimized for specific phases of your 6-phase implementation plan and the 8 AI services integration.

## Quick Setup

1. **Copy to your main project**:
   ```bash
   cp -r claude-subagents-config/.claude /path/to/your/main/project/
   ```

2. **Verify installation**:
   ```bash
   cd /path/to/your/main/project
   claude /agents
   ```

## Subagent Categories

### 🏗️ Phase-Specific Specialists

#### Phase 1: Foundation Setup
- **`python-microservice-architect.md`** - Creates Python microservice structure
- **`grpc-communication-specialist.md`** - Handles inter-service communication

#### Phase 2: LangChain Integration  
- **`langchain-orchestrator.md`** - Implements LangChain-based orchestration
- **`intelligent-router.md`** - Creates ML-powered service routing

#### Phase 3: Workflow Intelligence
- **`workflow-enhancer.md`** - Enhances visual workflow builder
- **`queue-bridge-specialist.md`** - Implements Celery-Bull bridge

#### Phase 4: Analytics Enhancement
- **`ml-analytics-specialist.md`** - Upgrades predictive analytics
- **`monitoring-architect.md`** - Builds hybrid monitoring system

#### Phase 5: Production Deployment
- **`docker-deployment-expert.md`** - Creates Docker Compose configuration
- **`migration-specialist.md`** - Handles migration and rollback strategies

#### Phase 6: Optimization
- **`performance-optimizer.md`** - Optimizes hybrid architecture performance
- **`integration-tester.md`** - Comprehensive integration testing

### 🔄 Cross-Cutting Specialists

#### Architecture & Integration
- **`hybrid-architecture-coordinator.md`** - Manages Node.js/Python integration
- **`ai-services-orchestrator.md`** - Handles 8 AI services coordination
- **`infrastructure-manager.md`** - Manages databases and message queues

#### Quality & Security
- **`security-auditor.md`** - Security across both stacks
- **`code-reviewer.md`** - Code quality for hybrid codebase
- **`test-automation-specialist.md`** - Testing strategies for both stacks

#### Documentation & DevOps
- **`documentation-specialist.md`** - Maintains comprehensive docs
- **`devops-engineer.md`** - CI/CD for hybrid architecture

## Usage Patterns

### Automatic Invocation
Subagents activate automatically based on context:
```
"I need to set up the Python microservice structure"
→ Activates python-microservice-architect

"Help me implement LangChain orchestration"  
→ Activates langchain-orchestrator
```

### Explicit Invocation
```
"Use the hybrid-architecture-coordinator to review the integration between Node.js and Python services"

"Have the security-auditor check the gRPC communication layer"

"Ask the performance-optimizer to analyze the current bottlenecks"
```

### Phase-Based Workflows
```
"Use the python-microservice-architect to start Phase 1 foundation setup"

"Have the docker-deployment-expert prepare Phase 5 production deployment"
```

## Integration with Your Tech Stack

### Node.js Backend
- TypeScript, Express, Apollo GraphQL
- PostgreSQL, Redis, Bull, RabbitMQ
- Existing TensorFlow.js analytics

### Python Microservices  
- FastAPI, LangChain, Celery
- SQLAlchemy, Redis integration
- ML models and analytics

### AI Services Integration
- Velian, ZeroEntropy, Hello.cv, YoinkUI
- Clueso, Permut, Intervo, Pixelesq
- Intelligent routing and orchestration

## MCP Tools Integration

These subagents are configured to work with your available MCP tools:
- **Desktop Commander** - File operations and system access
- **Memory Bank** - Project state management  
- **Knowledge Graph** - Pattern recognition and learning
- **Sequential Thinking** - Complex problem decomposition
- **Exa Search** - Technical research and documentation

## Best Practices

1. **Start with Foundation**: Begin with Phase 1 subagents
2. **Iterative Development**: Use one phase at a time
3. **Cross-Reference**: Let subagents collaborate on complex tasks
4. **Monitor Progress**: Use Memory Bank to track implementation status
5. **Document Decisions**: Leverage documentation specialist throughout

## Customization

Each subagent can be customized by editing:
- **Description**: Modify when the agent should activate
- **Tools**: Add/remove tool permissions
- **System Prompt**: Adjust behavior and expertise level

## Support

For issues or customizations:
1. Check the individual subagent documentation
2. Use the `/agents` command to verify configuration
3. Test with simple tasks before complex implementations

---

**Ready to transform your development workflow with specialized AI team members!** 🚀
